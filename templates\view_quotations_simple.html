<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            overflow: hidden;
            height: 100vh;
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 10px;
        }

        .title-version {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .version-tag {
            background: #e9ecef;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.9em;
            color: #495057;
        }

        .refresh-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 5px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: background-color 0.2s;
        }

        .refresh-btn:hover {
            background: #0056b3;
        }

        .dashboard-btn:hover {
            background: #218838 !important;
        }

        .quotation-table {
            width: 100%;
            min-width: 1400px;
            border-collapse: collapse;
            margin-top: 10px;
            background: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .quotation-table th,
        .quotation-table td {
            padding: 12px;
            border-bottom: 1px solid #dee2e6;
        }

        .quotation-table tr:hover {
            background-color: #f5f5f5;
        }

        .table-container {
            height: 500px;
            overflow: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: white;
            margin: 20px auto;
            max-width: 95%;
        }
        /* TOP Button Styles */
        .top-button {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            z-index: 1000;
            display: none;
            transition: all 0.3s ease;
            line-height: 1.2;
        }

        .top-button:hover {
            background: #0056b3;
            transform: scale(1.1);
        }

        /* Filter Row Styles */
        .filter-row {
            background: #f8f9fa !important;
            border-bottom: 2px solid #dee2e6 !important;
        }

        .filter-cell {
            padding: 8px !important;
            position: relative;
        }

        .filter-input {
            width: calc(100% - 4px);
            max-width: 100%;
            padding: 6px 25px 6px 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.85em;
            background: white;
            box-sizing: border-box;
            margin: 0;
        }

        .filter-input:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        .filter-dropdown {
            width: calc(100% - 4px);
            max-width: 100%;
            padding: 6px 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.85em;
            background: white;
            box-sizing: border-box;
            margin: 0;
        }

        .filter-dropdown:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        .advanced-arrow {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6c757d;
            font-size: 12px;
            user-select: none;
        }

        .advanced-arrow:hover {
            color: #007bff;
        }

        .advanced-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #ced4da;
            border-radius: 4px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            z-index: 1000;
            min-width: 150px;
            display: none;
        }

        .advanced-dropdown.show {
            display: block;
        }

        .advanced-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 0.85em;
            border-bottom: 1px solid #f1f3f4;
        }

        .advanced-option:last-child {
            border-bottom: none;
        }

        .advanced-option:hover {
            background: #f8f9fa;
        }

        .advanced-option.selected {
            background: #e3f2fd;
            color: #1976d2;
        }







        /* Zebra striping */
        .quotation-table tr:nth-child(even) {
            background-color: #fafafa;
        }

        /* Responsive design */
        @media (max-width: 1200px) {
            .table-container {
                margin: 0 10px;
            }

            .quotation-table th,
            .quotation-table td {
                padding: 8px;
                font-size: 0.9em;
            }
        }

        /* Creator email style */
        .creator-email {
            color: #666;
            font-size: 0.9em;
        }

        /* Timestamp styles */
        .timestamp {
            white-space: nowrap;
            color: #666;
            font-size: 0.9em;
        }

        /* Sticky/frozen columns */
        .quotation-table th.sticky,
        .quotation-table td.sticky {
            position: sticky;
            background: #fff !important;
            z-index: 100;
            background-clip: padding-box;
            border-right: 2px solid #fff;
        }
        .sticky-id { left: 0; z-index: 101; max-width: 70px; min-width: 60px; overflow: hidden; white-space: nowrap; background: #fff !important; }
        .sticky-customer { left: 70px; z-index: 102; max-width: 180px; min-width: 160px; overflow: hidden; white-space: nowrap; background: #fff !important; }
        .sticky-keyperson { left: 250px; z-index: 103; max-width: 200px; min-width: 180px; overflow: hidden; white-space: nowrap; background: #fff !important; }
        .sticky-itemcode { left: 450px; z-index: 104; max-width: 160px; min-width: 140px; overflow: hidden; white-space: nowrap; background: #fff !important; }
        .sticky-artwork { left: 610px; z-index: 105; max-width: 140px; min-width: 120px; overflow: hidden; background: #fff !important; }
        .sticky-status { left: 750px; z-index: 106; max-width: 100px; min-width: 80px; overflow: hidden; white-space: nowrap; background: #fff !important; box-shadow: 2px 0 6px -2px #bbb; }

        /* Status color coding */
        .status-quotation-complete {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb) !important;
            color: #1565c0 !important;
            border-left: 4px solid #2196f3 !important;
            font-weight: 500;
        }
        .status-submitted {
            background: linear-gradient(135deg, #fff3e0, #ffcc02) !important;
            color: #e65100 !important;
            border-left: 4px solid #ff9800 !important;
            font-weight: 500;
        }
        .status-price-approved {
            background: linear-gradient(135deg, #f3e5f5, #ce93d8) !important;
            color: #6a1b9a !important;
            border-left: 4px solid #9c27b0 !important;
            font-weight: 500;
        }
        .status-sampling {
            background: linear-gradient(135deg, #e8f5e8, #a5d6a7) !important;
            color: #2e7d32 !important;
            border-left: 4px solid #4caf50 !important;
            font-weight: 500;
        }
        .status-sample-card {
            background: linear-gradient(135deg, #fce4ec, #f8bbd9) !important;
            color: #ad1457 !important;
            border-left: 4px solid #e91e63 !important;
            font-weight: 500;
        }
        .sticky-action { left: 850px; z-index: 107; max-width: 100px; min-width: 80px; overflow: hidden; white-space: nowrap; background: #fff !important; box-shadow: 2px 0 6px -2px #bbb; }

        .quotation-table th {
            position: sticky;
            top: 0;
            z-index: 200;
            background: #fff !important;
            border-bottom: 2px solid #dee2e6;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="header-container">
        <div class="title-version">
            <h2 style="margin: 0;">Quotation Records</h2>
            <span class="version-tag">v1.5.03</span>
            {% if permission_level == 1 %}
                <span style="background:#ffc107;color:#212529;padding:4px 10px;border-radius:12px;font-size:0.85em;font-weight:500;">Level 1: Own Records Only</span>
            {% elif permission_level >= 3 %}
                <span style="background:#28a745;color:white;padding:4px 10px;border-radius:12px;font-size:0.85em;font-weight:500;">Level 3: All Records</span>
            {% else %}
                <span style="background:#dc3545;color:white;padding:4px 10px;border-radius:12px;font-size:0.85em;font-weight:500;">No Access</span>
            {% endif %}
        </div>
        <div style="display:flex;gap:10px;align-items:center;">
            <button class="dashboard-btn" onclick="if(window.parent && window.parent.loadDashboard) { window.parent.loadDashboard(); } else if(window.loadDashboard) { window.loadDashboard(); }" style="background:#28a745;color:white;border:none;padding:8px 16px;border-radius:5px;cursor:pointer;display:flex;align-items:center;gap:6px;transition:background-color 0.2s;">
                <span>📊</span>
                Dashboard
            </button>
            <button class="refresh-btn" onclick="loadData()">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M8 3a5 5 0 0 0-5 5H1l3.5 3.5L8 8H6a2 2 0 1 1 2 2v2a4 4 0 1 0-4-4H2a6 6 0 1 1 6 6v-2a4 4 0 0 0 0-8z"/>
                </svg>
                Refresh Data
            </button>
        </div>
    </div>

    <!-- User Info Display -->
    {% if user_email %}
    <div style="padding:10px 20px;background:#e9ecef;border-left:4px solid #007bff;margin:0 20px 20px 20px;border-radius:4px;">
        <strong>Logged in as:</strong> {{ user_email }}
        <span style="color:#666;">(Permission Level {{ permission_level }})</span>
        {% if permission_level == 1 %}
            - <em>You can only view quotations you created</em>
        {% elif permission_level >= 3 %}
            - <em>You can view all quotations</em>
        {% endif %}
    </div>
    {% endif %}

    <div class="table-container">
        <table class="quotation-table">
            <thead>
                <tr>
                    <th class="sticky sticky-id" style="left:0;top:0;z-index:201;background:#fff;">ID</th>
                    <th class="sticky sticky-customer" style="left:70px;top:0;z-index:202;background:#fff;">Customer</th>
                    <th class="sticky sticky-keyperson" style="left:250px;top:0;z-index:203;background:#fff;">Key Person</th>
                    <th class="sticky sticky-itemcode" style="left:450px;top:0;z-index:204;background:#fff;">Item Code</th>
                    <th class="sticky sticky-artwork" style="left:610px;top:0;z-index:205;background:#fff;">Artwork</th>
                    <th class="sticky sticky-status" style="left:750px;top:0;z-index:206;background:#fff;box-shadow:2px 0 6px -2px #bbb;">Status</th>
                    <th class="sticky sticky-action" style="left:850px;top:0;z-index:207;background:#fff;box-shadow:2px 0 6px -2px #bbb;">Action</th>
                    <th>Creator</th>
                    <th>Type</th>
                    <th>Quality</th>
                    <th>Flat/Raised</th>
                    <th>Direct/Reverse</th>
                    <th>Thickness</th>
                    <th># Colors</th>
                    <th>Length</th>
                    <th>Width</th>
                    <th>Price</th>
                    <th>Created</th>
                    <th>Last Updated</th>
                    <th>Revision</th>
                </tr>
                <!-- Filter Row -->
                <tr class="filter-row">
                    <th class="sticky sticky-id filter-cell" style="left:0;top:0;z-index:201;background:#f8f9fa;">
                        <input type="text" class="filter-input" id="filter-id" placeholder="ID">
                        <span class="advanced-arrow" data-filter="id">▼</span>
                        <div class="advanced-dropdown" id="advanced-id"></div>
                    </th>
                    <th class="sticky sticky-customer filter-cell" style="left:70px;top:0;z-index:202;background:#f8f9fa;">
                        <input type="text" class="filter-input" id="filter-customer" placeholder="Customer">
                        <span class="advanced-arrow" data-filter="customer">▼</span>
                        <div class="advanced-dropdown" id="advanced-customer"></div>
                    </th>
                    <th class="sticky sticky-keyperson filter-cell" style="left:250px;top:0;z-index:203;background:#f8f9fa;">
                        <input type="text" class="filter-input" id="filter-keyperson" placeholder="Key Person">
                        <span class="advanced-arrow" data-filter="keyperson">▼</span>
                        <div class="advanced-dropdown" id="advanced-keyperson"></div>
                    </th>
                    <th class="sticky sticky-itemcode filter-cell" style="left:450px;top:0;z-index:204;background:#f8f9fa;">
                        <input type="text" class="filter-input" id="filter-itemcode" placeholder="Item Code">
                        <span class="advanced-arrow" data-filter="itemcode">▼</span>
                        <div class="advanced-dropdown" id="advanced-itemcode"></div>
                    </th>
                    <th class="sticky sticky-artwork filter-cell" style="left:610px;top:0;z-index:205;background:#f8f9fa;">
                        <!-- No filter for artwork -->
                        <span style="color:#999;font-size:0.8em;">-</span>
                    </th>
                    <th class="sticky sticky-status filter-cell" style="left:750px;top:0;z-index:206;background:#f8f9fa;box-shadow:2px 0 6px -2px #bbb;">
                        <select class="filter-dropdown" id="filter-status">
                            <option value="">All Statuses</option>
                            <option value="quotation complete">Quotation Complete</option>
                            <option value="submitted to customer">Submitted to Customer</option>
                            <option value="price approved">Price Approved</option>
                            <option value="sampling">Sampling</option>
                            <option value="sample card">Sample Card</option>
                        </select>
                    </th>
                    <th class="sticky sticky-action filter-cell" style="left:850px;top:0;z-index:207;background:#f8f9fa;box-shadow:2px 0 6px -2px #bbb;">
                        <!-- No filter for action -->
                        <span style="color:#999;font-size:0.8em;">-</span>
                    </th>
                    <th class="filter-cell">
                        <input type="text" class="filter-input" id="filter-creator" placeholder="Creator">
                        <span class="advanced-arrow" data-filter="creator">▼</span>
                        <div class="advanced-dropdown" id="advanced-creator"></div>
                    </th>
                    <th class="filter-cell">
                        <input type="text" class="filter-input" id="filter-type" placeholder="Type">
                        <span class="advanced-arrow" data-filter="type">▼</span>
                        <div class="advanced-dropdown" id="advanced-type"></div>
                    </th>
                    <th class="filter-cell">
                        <input type="text" class="filter-input" id="filter-quality" placeholder="Quality">
                        <span class="advanced-arrow" data-filter="quality">▼</span>
                        <div class="advanced-dropdown" id="advanced-quality"></div>
                    </th>
                    <th class="filter-cell">
                        <input type="text" class="filter-input" id="filter-flat" placeholder="Flat/Raised">
                        <span class="advanced-arrow" data-filter="flat">▼</span>
                        <div class="advanced-dropdown" id="advanced-flat"></div>
                    </th>
                    <th class="filter-cell">
                        <input type="text" class="filter-input" id="filter-direct" placeholder="Direct/Reverse">
                        <span class="advanced-arrow" data-filter="direct">▼</span>
                        <div class="advanced-dropdown" id="advanced-direct"></div>
                    </th>
                    <th class="filter-cell"><span style="color:#999;font-size:0.8em;">-</span></th>
                    <th class="filter-cell"><span style="color:#999;font-size:0.8em;">-</span></th>
                    <th class="filter-cell"><span style="color:#999;font-size:0.8em;">-</span></th>
                    <th class="filter-cell"><span style="color:#999;font-size:0.8em;">-</span></th>
                    <th class="filter-cell"><span style="color:#999;font-size:0.8em;">-</span></th>
                    <th class="filter-cell"><span style="color:#999;font-size:0.8em;">-</span></th>
                    <th class="filter-cell"><span style="color:#999;font-size:0.8em;">-</span></th>
                    <th class="filter-cell"><span style="color:#999;font-size:0.8em;">-</span></th>
                </tr>
            </thead>
            <tbody id="quotation-table-body">
            </tbody>
        </table>

    </div>

    <!-- TOP Button -->
    <button id="top-button" class="top-button">TOP</button>

    <script>
        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleString();
        }

        function formatCreatorEmail(email) {
            if (!email) return '';
            const atIndex = email.indexOf('@');
            if (atIndex > 0) {
                return email.substring(0, atIndex);
            }
            return email;
        }

        function formatPrice(price) {
            if (price === null || price === undefined || price === '') return '-';
            return parseFloat(price).toFixed(2);
        }

        function createTableRows(data) {
            if (!data || data.length === 0) {
                // Get user permission level from template variables (if available)
                const permissionLevel = {{ permission_level|default(1) }};
                let message = 'No quotation records found.';

                if (permissionLevel === 1) {
                    message = 'No quotation records found that you created. Only quotations you create will appear here.';
                } else if (permissionLevel >= 3) {
                    message = 'No quotation records found in the system.';
                }

                return `<tr><td colspan="19" style="text-align: center; padding: 40px; color: #666; font-style: italic;">${message}</td></tr>`;
            }

            return data.map(row => {
                // Generate proper status display based on revision count and current status
                let displayStatus = row.status || 'quotation complete';
                const originalStatus = displayStatus; // Store original for CSS class
                if (displayStatus === 'quotation complete' && row.revision_count >= 1) {
                    displayStatus = `quotation complete (#${row.revision_count})`;
                }

                // Get status CSS class for color coding
                const statusClass = getStatusClass(originalStatus);

                // Add line breaks for long status text
                displayStatus = displayStatus.replace(/quotation complete/g, 'quotation<br>complete')
                                           .replace(/submitted to customer/g, 'submitted<br>price')
                                           .replace(/price approved/g, 'price<br>approved');

                return `
                <tr data-id="${row.id}">
                    <td class="sticky sticky-id">${row.id || ''}</td>
                    <td class="sticky sticky-customer">${row.customer_name || ''}</td>
                    <td class="sticky sticky-keyperson">${row.key_person_name || ''}</td>
                    <td class="sticky sticky-itemcode" style="font-family: monospace;">${row.customer_item_code || ''}</td>
                    <td class="sticky sticky-artwork">${row.artwork_image ? `<img src="/${row.artwork_image.replace(/^uploads\//, 'uploads/')}" style="max-width:100%;max-height:60px;object-fit:contain;display:block;margin:auto;border:1px solid #ccc;border-radius:4px;" loading="lazy">` : ''}</td>
                    <td class="sticky sticky-status status-cell ${statusClass}" data-original-status="${row.status || 'quotation complete'}" style="font-size:0.85em; padding: 8px 12px; text-align: center;">${displayStatus}</td>
                    <td class="sticky sticky-action" style="overflow:visible;"><button class='action-btn' data-id='${row.id}'>Action</button></td>
                    <td class="creator-email">${formatCreatorEmail(row.creator_email) || ''}</td>
                    <td>${row.type || 'Heat Transfer'}</td>
                    <td>${row.quality || ''}</td>
                    <td>${row.flat_or_raised || ''}</td>
                    <td>${row.direct_or_reverse || ''}</td>
                    <td>${row.thickness || ''}</td>
                    <td>${row.num_colors || ''}</td>
                    <td>${row.length || ''}</td>
                    <td>${row.width || ''}</td>
                    <td>${formatPrice(row.price)}</td>
                    <td class="timestamp">${formatDate(row.created_at)}</td>
                    <td class="timestamp">${formatDate(row.last_updated)}</td>
                    <td>${row.revision_count || 0}</td>
                </tr>
                `;
            }).join('');
        }

        function loadData() {
            fetch('/quotation/list')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error);
                    }

                    // Store all data for filtering globally
                    window.quotationAllData = data || [];

                    // Show only top 20 records initially
                    const displayData = window.quotationAllData.slice(0, 20);
                    document.getElementById('quotation-table-body').innerHTML = createTableRows(displayData);

                    // Initialize filters after data is loaded
                    setTimeout(() => {
                        if (window.initializeQuotationFilters) {
                            window.initializeQuotationFilters();
                        }
                    }, 100);

                    // Show pagination info
                    updatePaginationInfo();
                    updateLastUpdated();
                })
                .catch(error => {
                    console.error('Error loading data:', error);
                    document.getElementById('quotation-table-body').innerHTML =
                        `<tr><td colspan="19" style="text-align: center; color: red;">Error loading data: ${error.message}</td></tr>`;
                    updateLastUpdated();
                });
        }

        function updatePaginationInfo() {
            const totalRecords = window.quotationAllData ? window.quotationAllData.length : 0;
            const displayedRecords = Math.min(20, totalRecords);

            // Add pagination info if it doesn't exist
            let paginationDiv = document.getElementById('pagination-info');
            if (!paginationDiv) {
                paginationDiv = document.createElement('div');
                paginationDiv.id = 'pagination-info';
                paginationDiv.style.cssText = 'text-align: center; margin: 10px 0; color: #666; font-size: 0.9em;';
                document.querySelector('.table-container').appendChild(paginationDiv);
            }

            if (totalRecords > 20) {
                paginationDiv.innerHTML = `Showing top ${displayedRecords} of ${totalRecords} records. Use filters to narrow down results.`;
            } else {
                paginationDiv.innerHTML = `Showing all ${totalRecords} records.`;
            }
        }

        // Load data when page loads
        loadData();

        // Function to get status CSS class for color coding
        function getStatusClass(status) {
            const normalizedStatus = status.toLowerCase().replace(/\s+/g, ' ').trim();

            if (normalizedStatus.includes('sample card')) {
                return 'status-sample-card';
            } else if (normalizedStatus.includes('sampling')) {
                return 'status-sampling';
            } else if (normalizedStatus.includes('price approved')) {
                return 'status-price-approved';
            } else if (normalizedStatus.includes('submitted to customer')) {
                return 'status-submitted';
            } else if (normalizedStatus === 'quotation complete' || normalizedStatus.includes('quotation complete (#') ||
                       normalizedStatus === '-' || normalizedStatus === '' || !status) {
                return 'status-quotation-complete';
            }
            return 'status-quotation-complete'; // Default
        }

        // Function to determine button states based on current status
        function getButtonStates(currentStatus) {
            const disabledStyle = 'opacity:0.4;color:#999;cursor:not-allowed;';
            const activeStyle = '';

            console.log('[DEBUG] getButtonStates called with status:', currentStatus);

            // View is ALWAYS active regardless of status
            let states = {
                view: { text: 'View', style: activeStyle, disabled: false },
                submitted: { style: disabledStyle, disabled: true },
                priceApproved: { style: disabledStyle, disabled: true },
                startSampling: { style: disabledStyle, disabled: true },
                sampleCard: { style: disabledStyle, disabled: true }
            };

            // Normalize status for comparison (handle line breaks and extra spaces)
            const normalizedStatus = currentStatus.toLowerCase().replace(/\s+/g, ' ').trim();
            console.log('[DEBUG] Original status:', `"${currentStatus}"`);
            console.log('[DEBUG] Normalized status:', `"${normalizedStatus}"`);
            console.log('[DEBUG] Contains sampling?', normalizedStatus.includes('sampling'));

            if (normalizedStatus.includes('sample card')) {
                // Status 5: Sample Card completed - View and Sample Card active for re-download
                states.view = { text: 'View', style: activeStyle, disabled: false };
                states.submitted = { style: disabledStyle, disabled: true };
                states.priceApproved = { style: disabledStyle, disabled: true };
                states.startSampling = { style: disabledStyle, disabled: true };
                states.sampleCard = { text: 'Sample Card', style: activeStyle, disabled: false };
                console.log('[DEBUG] Status 5 - sample card completed (View and Sample Card active for re-download)');
            } else if (normalizedStatus.includes('sampling')) {
                // Status 4: Sampling active - View + Sample Card active, others disabled
                states.view = { text: 'View', style: activeStyle, disabled: false };
                states.submitted = { style: disabledStyle, disabled: true };
                states.priceApproved = { style: disabledStyle, disabled: true };
                states.startSampling = { style: disabledStyle, disabled: true };
                states.sampleCard = { style: activeStyle, disabled: false };
                console.log('[DEBUG] Status 4 - sampling (View + Sample Card active)');
            } else if (normalizedStatus === 'quotation complete' || normalizedStatus.includes('quotation complete (#') ||
                normalizedStatus === '-' || normalizedStatus === '' || !currentStatus) {
                // Status 1: View (edit) + Submit Price active (includes empty/null status)
                states.view = { text: 'View (edit)', style: activeStyle, disabled: false };
                states.submitted = { style: activeStyle, disabled: false };
                console.log('[DEBUG] Status 1 - quotation complete');
            } else if (normalizedStatus.includes('submitted to customer')) {
                // Status 2: View (read-only) + Price Approved active, others DISABLED
                states.view = { text: 'View', style: activeStyle, disabled: false };
                states.submitted = { style: disabledStyle, disabled: true };
                states.priceApproved = { style: activeStyle, disabled: false };
                states.startSampling = { style: disabledStyle, disabled: true };
                console.log('[DEBUG] Status 2 - submitted to customer');
            } else if (normalizedStatus.includes('price approved')) {
                // Status 3: View (read-only) + Start Sampling active, others DISABLED
                states.view = { text: 'View', style: activeStyle, disabled: false };
                states.submitted = { style: disabledStyle, disabled: true };
                states.priceApproved = { style: disabledStyle, disabled: true };
                states.startSampling = { style: activeStyle, disabled: false };
                console.log('[DEBUG] Status 3 - price approved');
            } else {
                // Default case: if status is empty or unknown, at least View should be active
                states.view = { text: 'View', style: activeStyle, disabled: false };
                console.log('[DEBUG] Default case - unknown status:', normalizedStatus);
            }

            console.log('[DEBUG] Final button states:', states);
            return states;
        }

        // Add JS to create a floating dropdown menu in the document body
        (function() {
            let openDropdown = null;
            let openBtn = null;
            function closeDropdown() {
                if (openDropdown) {
                    try {
                        document.body.removeChild(openDropdown);
                    } catch (e) {
                        // Element might already be removed
                    }
                    openDropdown = null;
                    openBtn = null;
                }
            }

            // Expose closeDropdown globally for external access
            window.closeQuotationDropdown = closeDropdown;

            // Close dropdown on various events
            document.addEventListener('click', function(e) {
                if (openDropdown && (!e.target.classList.contains('action-btn') && !e.target.classList.contains('action-dropdown-item'))) {
                    closeDropdown();
                }
            });
            document.addEventListener('scroll', closeDropdown, true);

            // Close dropdown when navigating away or page content changes
            window.addEventListener('beforeunload', closeDropdown);
            window.addEventListener('pagehide', closeDropdown);
            window.addEventListener('blur', closeDropdown);

            // Close dropdown on page visibility change
            document.addEventListener('visibilitychange', function() {
                if (document.hidden) {
                    closeDropdown();
                }
            });

            // Close dropdown on window resize (mobile orientation change, etc.)
            window.addEventListener('resize', closeDropdown);

            // Close dropdown when clicking on navigation links
            document.addEventListener('click', function(e) {
                if (e.target.tagName === 'A' || e.target.closest('a') ||
                    e.target.classList.contains('btn') || e.target.closest('.btn') ||
                    e.target.id === 'return-to-records' || e.target.closest('#return-to-records')) {
                    closeDropdown();
                }
            });

            // Close dropdown when right-frame content changes
            const rightFrame = document.getElementById('right-frame');
            if (rightFrame) {
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                            closeDropdown();
                        }
                    });
                });
                observer.observe(rightFrame, { childList: true, subtree: true });
            }

            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('action-btn')) {
                    closeDropdown();
                    openBtn = e.target;
                    const id = openBtn.getAttribute('data-id');
                    const rect = openBtn.getBoundingClientRect();
                    openDropdown = document.createElement('div');
                    openDropdown.className = 'action-dropdown';
                    openDropdown.style.position = 'fixed';
                    openDropdown.style.left = rect.left + 'px';
                    openDropdown.style.top = (rect.bottom + 4) + 'px';
                    openDropdown.style.background = '#fff';
                    openDropdown.style.border = '1px solid #ccc';
                    openDropdown.style.borderRadius = '6px';
                    openDropdown.style.boxShadow = '0 4px 16px rgba(0,0,0,0.18)';
                    openDropdown.style.zIndex = '99999';
                    openDropdown.style.minWidth = '180px';
                    // Get current status to determine button states
                    const currentRow = document.querySelector(`tr[data-id="${id}"]`);
                    const statusCell = currentRow ? currentRow.querySelector('.status-cell') : null;
                    const currentStatus = statusCell ? statusCell.getAttribute('data-original-status') || '' : '';
                    console.log('[DEBUG] Original status from data attribute:', currentStatus);
                    console.log('[DEBUG] Status cell element:', statusCell);

                    // Determine button states based on status
                    const buttonStates = getButtonStates(currentStatus);
                    console.log('[DEBUG] Button states for dropdown:', buttonStates);
                    console.log('[DEBUG] Creating dropdown for ID:', id, 'Status:', currentStatus);

                    openDropdown.innerHTML = `
                        <div class='action-dropdown-item' data-action='view' data-id='${id}' style='padding:10px 18px;cursor:pointer;${buttonStates.view.style}'>${buttonStates.view.text}</div>
                        <div class='action-dropdown-item' data-action='submitted-to-customer' data-id='${id}' style='padding:10px 18px;cursor:pointer;${buttonStates.submitted.style}' ${buttonStates.submitted.disabled ? 'data-disabled="true"' : ''}>Submit Price</div>
                        <div class='action-dropdown-item' data-action='price-approved' data-id='${id}' style='padding:10px 18px;cursor:pointer;${buttonStates.priceApproved.style}' ${buttonStates.priceApproved.disabled ? 'data-disabled="true"' : ''}>Price Approved</div>
                        <div class='action-dropdown-item' data-action='start-sampling' data-id='${id}' style='padding:10px 18px;cursor:pointer;${buttonStates.startSampling.style}' ${buttonStates.startSampling.disabled ? 'data-disabled="true"' : ''}>Start Sampling</div>
                        <div class='action-dropdown-item' data-action='sample-card' data-id='${id}' style='padding:10px 18px;cursor:pointer;${buttonStates.sampleCard.style}' ${buttonStates.sampleCard.disabled ? 'data-disabled="true"' : ''}>Sample Card</div>
                    `;
                    console.log('[DEBUG] Dropdown HTML created:', openDropdown.innerHTML);
                    document.body.appendChild(openDropdown);
                    e.stopPropagation();
                }
            });
            // Add handler for all action items (only once)
            if (!window.actionHandlerAdded) {
                window.actionHandlerAdded = true;
                document.addEventListener('click', function(e) {
                if (e.target.classList.contains('action-dropdown-item')) {
                    // Close dropdown immediately when any item is clicked
                    closeDropdown();

                    // Check if button is disabled
                    if (e.target.getAttribute('data-disabled') === 'true') {
                        return; // Do nothing for disabled buttons
                    }

                    const action = e.target.getAttribute('data-action');
                    const id = e.target.getAttribute('data-id');

                    if (action === 'view') {
                        // Pass status information to view function for edit restrictions
                        const currentRow = document.querySelector(`tr[data-id="${id}"]`);
                        const statusCell = currentRow ? currentRow.querySelector('.status-cell') : null;
                        const currentStatus = statusCell ? statusCell.getAttribute('data-original-status') || '' : '';
                        // Allow editing for Status 1 (including empty/null status)
                        const canEdit = (currentStatus === 'quotation complete' || currentStatus.startsWith('quotation complete (#') ||
                                        currentStatus === '-' || currentStatus === '' || !currentStatus);

                        if (typeof showQuotationViewForm2 === 'function') {
                            showQuotationViewForm2(id, null, canEdit);
                        } else {
                            window.open(`/quotation2_view_select?id=${id}`, '_blank');
                        }
                    } else if (action === 'submitted-to-customer') {
                        if (confirm('Are you sure you want to mark this quotation as submitted price?')) {
                            updateQuotationStatus(id, action);
                        }
                    } else if (action === 'price-approved') {
                        if (confirm('Are you sure you want to approve the price for this quotation?')) {
                            updateQuotationStatus(id, action);
                        }
                    } else if (action === 'start-sampling') {
                        if (confirm('Are you sure you want to start sampling for this quotation?')) {
                            updateQuotationStatus(id, action);
                        }
                    } else if (action === 'sample-card') {
                        if (confirm('Open Sample Card? This will update the status to "sample card".')) {
                            // Update status to "sample card" and then download PDF
                            updateQuotationStatus(id, action);
                        }
                    }
                }
                });
            }
        })();

        // Function to generate and download Sample Card file
        function generateSampleCardPDF(quotationId) {
            console.log(`[DEBUG] Opening Sample Card for quotation ${quotationId}`);

            // Open Sample Card in new tab
            const url = `/quotation/sample-card/${quotationId}`;
            window.open(url, '_blank');

            console.log('[DEBUG] Sample Card opened in new tab');
        }

        // Function to update quotation status via API
        function updateQuotationStatus(quotationId, action) {
            console.log(`[DEBUG] Updating quotation ${quotationId} with action: ${action}`);

            fetch(`/quotation/status/${quotationId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ action: action })
            })
            .then(response => {
                console.log(`[DEBUG] Response status: ${response.status}`);
                return response.json();
            })
            .then(data => {
                console.log('[DEBUG] Response data:', data);
                if (data.error) {
                    alert('Error updating status: ' + data.error);
                } else {
                    // Success - reload the data to reflect changes
                    console.log('[DEBUG] Status updated successfully, reloading data...');
                    loadData();

                    // Special handling for sample-card action: trigger PDF download after status update
                    if (action === 'sample-card') {
                        console.log('[DEBUG] Sample card status updated, triggering PDF download...');
                        generateSampleCardPDF(quotationId);
                        alert('Status updated to "sample card" and Sample Card opened!');
                    } else {
                        alert('Status updated successfully!');
                    }
                }
            })
            .catch(error => {
                console.error('Error updating status:', error);
                alert('Error updating status. Please try again.');
            });
        }

        // Add last updated info with access control details
        function updateLastUpdated() {
            const now = new Date().toLocaleString();
            const permissionLevel = {{ permission_level|default(1) }};
            const userEmail = '{{ user_email|default("") }}';

            let accessInfo = '';
            if (permissionLevel === 1) {
                accessInfo = ` | Showing only quotations created by ${userEmail}`;
            } else if (permissionLevel >= 3) {
                accessInfo = ' | Showing all quotations (Admin access)';
            }

            document.getElementById('last-updated').innerHTML =
                `<div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; color: #666; font-size: 0.9em;">
                    <strong>Last updated:</strong> ${now}${accessInfo}
                    <br><small>Version v1.5.03 | Access Level: ${permissionLevel}</small>
                </div>`;
        }

        // Filter functionality - wrapped in IIFE to avoid conflicts
        (function() {
            // Check if already initialized to prevent double initialization
            if (window.quotationFiltersInitialized) {
                return;
            }
            window.quotationFiltersInitialized = true;

            const quotationFilterTypes = {
                contains: 'Contains',
                startswith: 'Starts with',
                endswith: 'Ends with',
                exact: 'Exact match',
                and: 'Multiple (AND)',
                or: 'Multiple (OR)'
            };

            let quotationCurrentFilterTypes = {};

        // Initialize filter system
        window.initializeQuotationFilters = function() {
            try {
                console.log('[DEBUG] Initializing filters...');

                // Set default filter types
                ['id', 'customer', 'keyperson', 'itemcode', 'creator', 'type', 'quality', 'flat', 'direct'].forEach(field => {
                    quotationCurrentFilterTypes[field] = 'contains';
                });

            // Create advanced dropdown content
            Object.keys(quotationFilterTypes).forEach(filterKey => {
                ['id', 'customer', 'keyperson', 'itemcode', 'creator', 'type', 'quality', 'flat', 'direct'].forEach(field => {
                    const dropdown = document.getElementById(`advanced-${field}`);
                    if (dropdown) {
                        dropdown.innerHTML = Object.entries(quotationFilterTypes).map(([key, label]) =>
                            `<div class="advanced-option ${key === 'contains' ? 'selected' : ''}" data-type="${key}" data-field="${field}">${label}</div>`
                        ).join('');
                    }
                });
            });

            // Add event listeners for advanced arrows
            document.querySelectorAll('.advanced-arrow').forEach(arrow => {
                arrow.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const field = this.getAttribute('data-filter');
                    const dropdown = document.getElementById(`advanced-${field}`);

                    // Close all other dropdowns
                    document.querySelectorAll('.advanced-dropdown').forEach(dd => {
                        if (dd !== dropdown) dd.classList.remove('show');
                    });

                    // Toggle current dropdown
                    dropdown.classList.toggle('show');
                });
            });

            // Add event listeners for dropdown options
            document.querySelectorAll('.advanced-option').forEach(option => {
                option.addEventListener('click', function() {
                    const field = this.getAttribute('data-field');
                    const type = this.getAttribute('data-type');

                    // Update current filter type
                    quotationCurrentFilterTypes[field] = type;

                    // Update selected state
                    const dropdown = document.getElementById(`advanced-${field}`);
                    dropdown.querySelectorAll('.advanced-option').forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');

                    // Close dropdown
                    dropdown.classList.remove('show');

                    // Apply filters
                    window.applyQuotationFilters();
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function() {
                document.querySelectorAll('.advanced-dropdown').forEach(dd => dd.classList.remove('show'));
            });

            // Add event listeners for filter inputs
            ['id', 'customer', 'keyperson', 'itemcode', 'creator', 'type', 'quality', 'flat', 'direct'].forEach(field => {
                const input = document.getElementById(`filter-${field}`);
                if (input) {
                    console.log(`[DEBUG] Adding event listener to filter-${field}`);
                    input.addEventListener('input', window.applyQuotationFilters);
                } else {
                    console.log(`[DEBUG] Could not find element filter-${field}`);
                }
            });

            // Add event listener for status dropdown
            const statusSelect = document.getElementById('filter-status');
            if (statusSelect) {
                statusSelect.addEventListener('change', window.applyQuotationFilters);
            }

            console.log('[DEBUG] Filters initialized successfully');
            } catch (error) {
                console.error('[ERROR] Failed to initialize filters:', error);
            }
        }

        // Apply filters function
        window.applyQuotationFilters = function() {
            try {
                console.log('[DEBUG] applyQuotationFilters called');
                if (!window.quotationAllData || window.quotationAllData.length === 0) {
                    console.log('[DEBUG] No data available for filtering');
                    return;
                }

            const filters = {
                id: document.getElementById('filter-id')?.value || '',
                customer: document.getElementById('filter-customer')?.value || '',
                keyperson: document.getElementById('filter-keyperson')?.value || '',
                itemcode: document.getElementById('filter-itemcode')?.value || '',
                status: document.getElementById('filter-status')?.value || '',
                creator: document.getElementById('filter-creator')?.value || '',
                type: document.getElementById('filter-type')?.value || '',
                quality: document.getElementById('filter-quality')?.value || '',
                flat: document.getElementById('filter-flat')?.value || '',
                direct: document.getElementById('filter-direct')?.value || ''
            };

            const filteredData = window.quotationAllData.filter(row => {
                return Object.entries(filters).every(([field, value]) => {
                    if (!value) return true; // No filter applied

                    let rowValue = '';
                    switch(field) {
                        case 'id': rowValue = String(row.id || ''); break;
                        case 'customer': rowValue = String(row.customer_name || ''); break;
                        case 'keyperson': rowValue = String(row.key_person_name || ''); break;
                        case 'itemcode': rowValue = String(row.customer_item_code || ''); break;
                        case 'status': rowValue = String(row.status || 'quotation complete'); break;
                        case 'creator': rowValue = String(row.creator_email || ''); break;
                        case 'type': rowValue = String(row.type || 'Heat Transfer'); break;
                        case 'quality': rowValue = String(row.quality || ''); break;
                        case 'flat': rowValue = String(row.flat_or_raised || ''); break;
                        case 'direct': rowValue = String(row.direct_or_reverse || ''); break;
                    }

                    return matchesFilter(rowValue, value, quotationCurrentFilterTypes[field] || 'contains');
                });
            });

            // Update table with filtered data
            document.getElementById('quotation-table-body').innerHTML = createTableRows(filteredData);

            // Update pagination info for filtered results
            window.updateFilteredPaginationInfo(filteredData.length);
            } catch (error) {
                console.error('[ERROR] Failed to apply filters:', error);
            }
        }

        // Check if value matches filter based on filter type
        function matchesFilter(rowValue, filterValue, filterType) {
            if (!filterValue) return true;

            rowValue = rowValue.toLowerCase();
            filterValue = filterValue.toLowerCase();

            switch(filterType) {
                case 'contains':
                    return rowValue.includes(filterValue);
                case 'startswith':
                    return rowValue.startsWith(filterValue);
                case 'endswith':
                    return rowValue.endsWith(filterValue);
                case 'exact':
                    return rowValue === filterValue;
                case 'and':
                    // Split by AND, &, or comma and check all terms
                    const andTerms = filterValue.split(/\s+and\s+|\s*&\s*|\s*,\s+/).filter(term => term.trim());
                    return andTerms.every(term => rowValue.includes(term.trim()));
                case 'or':
                    // Split by OR, |, or comma and check any term
                    const orTerms = filterValue.split(/\s+or\s+|\s*\|\s*|\s*,\s+/).filter(term => term.trim());
                    return orTerms.some(term => rowValue.includes(term.trim()));
                default:
                    return rowValue.includes(filterValue);
            }
        }

        window.updateFilteredPaginationInfo = function(filteredCount) {
            const paginationDiv = document.getElementById('pagination-info');
            if (paginationDiv) {
                const totalRecords = window.quotationAllData ? window.quotationAllData.length : 0;
                if (filteredCount === totalRecords) {
                    if (totalRecords > 20) {
                        paginationDiv.innerHTML = `Showing top 20 of ${totalRecords} records. Use filters to narrow down results.`;
                    } else {
                        paginationDiv.innerHTML = `Showing all ${totalRecords} records.`;
                    }
                } else {
                    paginationDiv.innerHTML = `Showing ${filteredCount} filtered records (of ${totalRecords} total).`;
                }
            }
        }

        })(); // End of IIFE to prevent variable conflicts

        // TOP button functionality
        document.addEventListener('DOMContentLoaded', function() {
            const topButton = document.getElementById('top-button');
            const tableContainer = document.querySelector('.table-container');

            if (topButton && tableContainer) {
                // Show/hide TOP button based on scroll position
                tableContainer.addEventListener('scroll', function() {
                    if (tableContainer.scrollTop > 200) {
                        topButton.style.display = 'block';
                    } else {
                        topButton.style.display = 'none';
                    }
                });

                // Scroll to first row of table when button is clicked
                topButton.addEventListener('click', function() {
                    const firstRow = tableContainer.querySelector('table thead tr');
                    if (firstRow) {
                        // Scroll to the table header (first row)
                        tableContainer.scrollTo({
                            top: 0,
                            behavior: 'smooth'
                        });
                    }
                });
            }
        });

    </script>

    <div id="last-updated"></div>
</body>
</html>
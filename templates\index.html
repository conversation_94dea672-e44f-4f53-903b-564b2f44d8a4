<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="csrf-token" content="{{ csrf_token }}">
  <title>Customer Management App</title>
  <link rel="stylesheet" href="/static/css/style.css">
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
  <script src="/static/js/app.js"></script>
  <script src="/static/js/quotation_create.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf_viewer.min.css"/>
  <style>
    .container {
      display: flex;
      min-height: 100vh;
    }
    .left-frame {
      width: 220px;
      background: #f4f8fb;
      padding: 18px 0 0 0;
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      z-index: 100;
      box-shadow: 2px 0 8px -2px #ddd;
    }
    .right-frame {
      flex: 1;
      margin-left: 220px;
      padding: 0;
      min-height: 100vh;
      background: #fff;
    }
  </style>
  <script>
    // Define permission level in global scope
    var userPermissionLevel = "{{ permission_level }}";
    // Define global version
    var appVersion = "v1.5.03";
    // Global variables
  </script>
</head>
<body>
  <!-- User email display -->
  <div style="position:fixed;top:18px;right:32px;z-index:1000;font-size:16px;color:#444;background:#f7f7f7;padding:6px 18px;border-radius:18px;box-shadow:0 2px 8px #eee;">
    {% if user_email %}
      Logged in as <b>{{ user_email }}</b> (level {{ permission_level }})
    {% endif %}
  </div>
  <div class="container">
    <div class="left-frame">
      <button id="btn-dashboard" style="background:#28a745;color:white;font-weight:bold;margin-bottom:8px;">📊 Dashboard</button>
      <button id="btn-customer">Customer</button>
      <div id="customer-nested" style="display:none; margin-left: 10px;">
        <button id="btn-create">Create</button>
        <button id="btn-modify">Modify</button>
      </div>
      <button id="btn-outsource">Outsource</button>
      <div id="outsource-nested" style="display:none; margin-left: 10px;">
        <button id="btn-outsource-create">Create</button>
        <button id="btn-outsource-modify">Modify</button>
      </div>
      <!-- Removed Quotation and Development buttons as per v1.2.81 -->
      <!-- <button id="btn-quotation">Quotation</button> -->
      <!-- <div id="quotation-nested" style="display:none; margin-left: 10px;">
        <button id="btn-quotation-create">Create</button>
      </div> -->
      <!-- <button id="btn-development">Development</button> -->
      <!-- Admin button only for level 3 users -->
      <button id="btn-admin" style="display:none;">Admin</button>
      <button id="btn-quotation2">Quotation 2</button>
      <div id="quotation2-nested" style="display:none; margin-left: 10px;">
        <button id="btn-quotation2-create2">Create (HT)</button>
        <button id="btn-view-quotations">View Records</button>
      </div>
      <div style="position:absolute;bottom:40px;left:30px;width:160px;text-align:center;">
        <button id="logout-btn" style="padding:10px 32px; background:#eee; border-radius:6px; border:1px solid #ccc; font-size:18px;">Logout</button>
      </div>
    </div>
    <div class="right-frame" id="right-frame">
      <!-- Dynamic content goes here -->
      <div id="dashboard-default" style="padding:40px;text-align:center;">
        <!-- Dashboard will be loaded here by default -->
        <div style="color:#7f8c8d;font-size:1.1rem;">
          <div style="margin-bottom:20px;">📊 Loading Dashboard v1.4.01...</div>
          <div style="font-size:0.9rem;">Please wait while we load your dashboard metrics</div>
          <div style="margin-top:20px;">
            <button onclick="loadFallbackDashboard()" style="background:#3498db;color:white;border:none;padding:8px 18px;border-radius:5px;font-size:15px;cursor:pointer;">Force Load Dashboard</button>
          </div>
        </div>
      </div>
      <!-- Static Quotation Create UI (no JS, no rules, just UI) -->
      <div id="quotation-create-form" style="display:none; padding:32px;max-width:600px; margin-top:40px;">
        <h2>Create Quotation</h2>
        <form autocomplete="off">
          <label>Company:<br><input type="text" name="quotation-company" value=""></label><br><br>
          <label>Key Person:<br>
            <select name="quotation-keyperson">
              <option value="">-- Select --</option>
            </select>
          </label><br><br>
          <label>Product Type:<br>
            <select name="quotation-product-type">
              <option value="">-- Select --</option>
            </select>
          </label><br><br>
          <label>Quality:<br>
            <select name="quality">
              <option value="">-- Select --</option>
            </select>
          </label><br><br>
          <label>Flat or Raised:<br>
            <select name="flatOrRaised">
              <option value="">-- Select --</option>
            </select>
          </label><br><br>
          <label>Direct or Reverse:<br>
            <select name="directOrReverse">
              <option value="">-- Select --</option>
              <option value="Direct">Direct</option>
              <option value="Reverse">Reverse</option>
            </select>
          </label><br><br>
          <label>Thickness:<br><input type="text" name="thickness" value=""></label><br><br>
          <label># of Colors:<br><input type="text" name="numColors" value=""></label><br><br>
          <label>Width:<br><input type="text" name="width" value=""></label><br><br>
          <label>Length:<br><input type="text" name="length" value=""></label><br><br>
          <button type="submit">Submit</button>
          <button type="button" style="margin-left:10px;background:red;color:white;padding:5px 10px;">DB</button>
        </form>
      </div>
      <!-- Static Quotation 2 Create 2 UI -->
      <div id="quotation2-create2-form" style="display:none; padding:32px;max-width:600px; margin-top:40px;">
        <h2>Create Quotation (HT) <span style='font-size:1rem;color:#888;'>v1.2.78</span></h2>
        {% if permission_level >= 3 %}
        <div style="background-color: #f0f8ff; border: 2px solid #4a90e2; padding: 15px; margin: 15px 0; border-radius: 8px; text-align: center;">
          <button type="button" id="btn-ht-database" style="background-color: #4a90e2; color: white; font-size: 18px; padding: 10px 30px; border: none; border-radius: 6px; cursor: pointer; font-weight: bold; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            DATABASE
          </button>
          <div style="margin-top: 8px; font-size: 12px; color: #666;">Access HT Database (Level 3 users only)</div>
        </div>
        {% endif %}
        <div style="text-align: center; margin: 20px 0;">
          <button type="button" id="btn-quotation2-create2-js" style="background-color: #3498db; color: white; font-size: 16px; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">
            Create Quotation (HT)
          </button>
        </div>
        <label style="font-weight:bold;">Upload JPG/PNG Artwork:<br><span style='font-weight:normal;font-size:13px;color:#888;'>(Drag & drop, click, or <b>Ctrl+V</b> to paste JPG/PNG or screenshot)</span></label>
      </div>
      <h2>View Quotation <span style="font-size:1rem;color:#888;">v1.0.2</span></h2>
    </div>
  </div>
  <div style="position:fixed;bottom:10px;right:20px;font-size:13px;color:#888;z-index:1000;">Version v1.2.81</div>
  
  <script>
    // Show Admin button only for level 3 users
    if (userPermissionLevel >= 3) {
      document.getElementById('btn-admin').style.display = 'block';
    }
    
    // Admin button click handler
    document.getElementById('btn-admin').onclick = function() {
      window.open('/admin', '_blank');
    }
    
    // Logout button click handler
    document.getElementById('logout-btn').onclick = function() {
      window.location = '/logout';
    }

    // Toggle Quotation 2 menu
    $('#btn-quotation2').on('click', function() {
      $('#quotation2-nested').toggle();
    });

    // Toggle Outsource menu
    $('#btn-outsource').on('click', function() {
      $('#outsource-nested').toggle();
    });
    
    // Handle Quotation 2 Create (HT) button click
    $('#btn-quotation2-create2').on('click', function() {
      showQuotationCreateForm2();
    });

    // Fallback dashboard function
    function loadFallbackDashboard() {
      console.log('[INDEX v1.4.01] Loading fallback dashboard...');

      // Clear any existing content
      $('#right-frame').empty();

      // Get dashboard data via AJAX
      $.ajax({
        url: '/dashboard?format=json',
        method: 'GET',
        success: function(data) {
          const dashboardHtml = `
            <div style="padding:30px;font-family:Arial,sans-serif;background:#f7fafd;min-height:400px;position:relative;">
              <div style="position:absolute;top:10px;right:15px;font-size:0.75rem;color:#999;background:#f0f0f0;padding:3px 8px;border-radius:3px;">v1.4.03</div>
              <div style="text-align:center;margin-bottom:25px;">
                <h1 style="color:#333;font-size:1.8rem;margin-bottom:5px;font-weight:normal;">📊 Dashboard</h1>
                <div style="color:#666;font-size:0.9rem;">Business metrics overview</div>
                <div id="fallback-access-info" style="margin-top:10px;font-size:0.85rem;color:#666;"></div>
              </div>

              <div style="display:grid;grid-template-columns:repeat(auto-fit,minmax(240px,1fr));gap:15px;max-width:1000px;margin:0 auto;">
                <div style="background:#3498db;border-radius:5px;padding:20px;text-align:center;color:white;box-shadow:0 2px 8px rgba(0,0,0,0.08);border:1px solid #2980b9;transition:all 0.2s;cursor:pointer;" onmouseover="this.style.background='#217dbb'" onmouseout="this.style.background='#3498db'">
                  <div style="font-size:2rem;margin-bottom:10px;">📋</div>
                  <div style="font-size:2.2rem;font-weight:bold;margin-bottom:6px;">${data.total_quotations || 0}</div>
                  <div style="font-size:0.9rem;font-weight:normal;text-transform:uppercase;letter-spacing:0.3px;">Total Quotations</div>
                </div>

                <div style="background:#9b59b6;border-radius:5px;padding:20px;text-align:center;color:white;box-shadow:0 2px 8px rgba(0,0,0,0.08);border:1px solid #8e44ad;transition:all 0.2s;cursor:pointer;" onmouseover="this.style.background='#8e44ad'" onmouseout="this.style.background='#9b59b6'">
                  <div style="font-size:2rem;margin-bottom:10px;">📤</div>
                  <div style="font-size:2.2rem;font-weight:bold;margin-bottom:6px;">${data.submitted_to_customer || 0}</div>
                  <div style="font-size:0.9rem;font-weight:normal;text-transform:uppercase;letter-spacing:0.3px;">Submitted Price</div>
                </div>

                <div style="background:#27ae60;border-radius:5px;padding:20px;text-align:center;color:white;box-shadow:0 2px 8px rgba(0,0,0,0.08);border:1px solid #229954;transition:all 0.2s;cursor:pointer;" onmouseover="this.style.background='#229954'" onmouseout="this.style.background='#27ae60'">
                  <div style="font-size:2rem;margin-bottom:10px;">✅</div>
                  <div style="font-size:2.2rem;font-weight:bold;margin-bottom:6px;">${data.price_approved || 0}</div>
                  <div style="font-size:0.9rem;font-weight:normal;text-transform:uppercase;letter-spacing:0.3px;">Price Approved</div>
                </div>

                <div style="background:#e67e22;border-radius:5px;padding:20px;text-align:center;color:white;box-shadow:0 2px 8px rgba(0,0,0,0.08);border:1px solid #d35400;transition:all 0.2s;cursor:pointer;" onmouseover="this.style.background='#d35400'" onmouseout="this.style.background='#e67e22'">
                  <div style="font-size:2rem;margin-bottom:10px;">🔬</div>
                  <div style="font-size:2.2rem;font-weight:bold;margin-bottom:6px;">${data.sampling || 0}</div>
                  <div style="font-size:0.9rem;font-weight:normal;text-transform:uppercase;letter-spacing:0.3px;">Sampling</div>
                </div>

                <div style="background:#e91e63;border-radius:5px;padding:20px;text-align:center;color:white;box-shadow:0 2px 8px rgba(0,0,0,0.08);border:1px solid #ad1457;transition:all 0.2s;cursor:pointer;" onmouseover="this.style.background='#ad1457'" onmouseout="this.style.background='#e91e63'">
                  <div style="font-size:2rem;margin-bottom:10px;">🎯</div>
                  <div style="font-size:2.2rem;font-weight:bold;margin-bottom:6px;">${data.sample_card || 0}</div>
                  <div style="font-size:0.9rem;font-weight:normal;text-transform:uppercase;letter-spacing:0.3px;">Sample Card</div>
                </div>
              </div>

              <div style="text-align:center;margin-top:25px;">
                <button onclick="loadFallbackDashboard()" style="background:#3498db;color:white;border:none;padding:8px 18px;border-radius:5px;font-size:15px;cursor:pointer;transition:background 0.2s;" onmouseover="this.style.background='#217dbb'" onmouseout="this.style.background='#3498db'">🔄 Refresh Data</button>
              </div>

              <div style="text-align:center;margin-top:20px;color:#666;font-size:0.85rem;">
                Last updated: ${new Date().toLocaleString()}
              </div>

              <div style="text-align:center;margin-top:10px;font-size:0.8rem;color:#999;">
                Dashboard v1.4.03 | Fallback Mode
              </div>
            </div>
          `;

          $('#right-frame').html(dashboardHtml);

          // Update access control information in fallback dashboard
          if (data.access_level && data.filtered_for) {
            let accessBadge = '';
            let accessText = '';

            if (data.access_level === 1) {
              accessBadge = '<span style="background:#ffc107;color:#212529;padding:4px 10px;border-radius:12px;font-size:0.8rem;font-weight:500;margin-left:10px;">Level 1: Own Records Only</span>';
              accessText = `Showing metrics for quotations created by: ${data.filtered_for}`;
            } else if (data.access_level >= 3) {
              accessBadge = '<span style="background:#28a745;color:white;padding:4px 10px;border-radius:12px;font-size:0.8rem;font-weight:500;margin-left:10px;">Level 3: All Records</span>';
              accessText = 'Showing metrics for all quotations in the system';
            } else {
              accessBadge = '<span style="background:#dc3545;color:white;padding:4px 10px;border-radius:12px;font-size:0.8rem;font-weight:500;margin-left:10px;">No Access</span>';
              accessText = 'No access to quotation data';
            }

            $('#fallback-access-info').html(accessBadge + '<br><small style="color:#888;">' + accessText + '</small>');
          }
        },
        error: function(xhr, status, error) {
          console.error('Fallback dashboard error:', error);
          $('#right-frame').html(`
            <div style="padding:40px;text-align:center;color:#e74c3c;">
              <h2>❌ Dashboard Error</h2>
              <p>Unable to load dashboard data</p>
              <p>Status: ${xhr.status} ${xhr.statusText}</p>
              <button onclick="location.reload()" style="background:#e74c3c;color:white;border:none;padding:12px 24px;border-radius:6px;font-size:1rem;cursor:pointer;margin-top:20px;">Refresh Page</button>
            </div>
          `);
        }
      });
    }

    // Add handler for View Records button
    $(document).ready(function() {
      // Load dashboard by default when page loads
      console.log('[INDEX v1.4.01] Loading dashboard...');

      // Force clear any cached content first
      $('#right-frame').empty();

      // Set a timeout to force fallback dashboard if main doesn't load
      var dashboardTimeout = setTimeout(function() {
        console.log('[INDEX] Dashboard load timeout - forcing fallback');
        loadFallbackDashboard();
      }, 3000);

      // Try to load main dashboard
      $('#right-frame').load('/dashboard', function(response, status, xhr) {
        clearTimeout(dashboardTimeout); // Cancel timeout since we got a response

        if (status == "error") {
          console.error('[INDEX] Dashboard load error:', xhr.status, xhr.statusText);
          console.log('[INDEX] Falling back to inline dashboard...');
          // Load fallback dashboard immediately
          loadFallbackDashboard();
        } else {
          console.log('[INDEX] Dashboard loaded successfully');
          console.log('[INDEX] Response length:', response.length);

          // Check if we actually got dashboard content
          if (response.length < 100 || !response.includes('dashboard')) {
            console.warn('[INDEX] Dashboard response seems invalid, using fallback');
            loadFallbackDashboard();
          }
        }
      });

      $('#btn-view-quotations').click(function() {
        $('#right-frame').load('/view_quotations_simple');
      });
    });
  </script>
</body>
</html>

<!-- Removed old LocalStorage and global scope JS code. The logic is now handled in app.js via AJAX and backend. --> 